import React, { useEffect, useRef, useState } from 'react';
import { string, node, bool, object, number, oneOfType, oneOf } from 'prop-types';
import omit from 'lodash/omit';

import './styles.scss';
import TooltipCard from './TooltipCard';
import { useWindowSize } from '../Utils/hooks';
import remCalc from '../Utils/remCalc';

// Tooltip accessibility props are optional props which we can use as our requirement
// Pleae refer below docs to understand why, when and how we use below attributes
// tabIndex  : https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/tabindex
// ariaLabel : https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes/aria-label
// arialabelledby : https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes/aria-labelledby
// ariaDescribedby : https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes/aria-describedby

// Dev Notes:
//     - If an element has both aria-label and aria-labelledby attributes, then only aria-labelledby will be used.
//     - Interactive elements(focusable elements) must be passed as children , else if we don't have any interactive elements in children then to support accessibility we can pass tabIndex = 0

const Tooltip = ({
  message,
  isHtmlIncluded,
  pos = 'bottom',
  children,
  hide = false,
  show = false,
  id = 'tooltipCard',
  margin,
  tooltipCardInlineCss,
  tooltipCardCustomClass,
  scrollTooltipRelativeElement,
  onScrollHideTooltipCard = false,
  useOnPointerLeave = false,
  // onMouseLeave doesn't work when children element is disabled therefore using `onPointerLeave` as workaround fix for this specific issue
  customClass = '',
  tooltipParentRole,
  tabIndex = '-1',
  ariaLabel,
  arialabelledby,
  ariaDescribedby,
  ariaHidden,
  isTranslated
}) => {
  const [showTooltip, setShowTooltip] = useState(false);
  const tooltipParentEelementRef = useRef();
  const screenSize = useWindowSize();
  const isSmallerScreen = screenSize.width <= 500;

  useEffect(() => {
    setShowTooltip(show);
  }, [show]);

  useEffect(() => {
    const onKeyDownHandler = event => {
      if (event.key === 'Escape') {
        setShowTooltip(false);
      }
    };
    document.addEventListener('keydown', onKeyDownHandler);
    return () => {
      document.removeEventListener('keydown', onKeyDownHandler);
    };
  }, []);

  const mouseEnterHandler = (e) => {
    // Prevent event bubbling to parent containers
    e.stopPropagation();
    if (!hide) {
      setShowTooltip(true);
    }
  };
  const mouseLeaveHandler = (e) => {
    // Prevent event bubbling to parent containers
    e.stopPropagation();
    setShowTooltip(false);
  };

  // Used to dismiss tooltip popup when escape keyboard button pressed
  const onKeyDownHandler = event => {
    if (event.key === 'Escape') {
      mouseLeaveHandler();
    }
  };
  const onPointerLeaveHandler = useOnPointerLeave && { onPointerLeave: mouseLeaveHandler };
  const tooltipPosition = isSmallerScreen ? 'bottom' : pos || 'bottom';

  return (
    <div
      className={`ed-tooltip ${tooltipPosition} justify-center relative inlineflex ${customClass}`}
      onMouseEnter={mouseEnterHandler}
      onMouseLeave={mouseLeaveHandler}
      {...onPointerLeaveHandler}
      ref={tooltipParentEelementRef}
      role={tooltipParentRole}
      tabIndex={tabIndex}
      onFocus={mouseEnterHandler}
      onBlur={mouseLeaveHandler}
      onKeyDown={onKeyDownHandler}
      aria-labelledby={arialabelledby}
      aria-describedby={ariaDescribedby}
      aria-hidden={ariaHidden && !showTooltip}
      id={id}
    >
      {showTooltip && !hide && (
        <TooltipCard
          id={id}
          openTooltipCard={showTooltip}
          message={message}
          isHtmlIncluded={isHtmlIncluded}
          pos={tooltipPosition}
          parentEelement={tooltipParentEelementRef.current}
          margin={margin}
          tooltipCardInlineCss={{
            ...omit(tooltipCardInlineCss, 'max-width'),
            maxWidth: isSmallerScreen ? remCalc(300) : tooltipCardInlineCss?.['max-width'],
            cursor: 'default'
          }}
          ariaLabel={ariaLabel}
          tooltipCardCustomClass={tooltipCardCustomClass}
          scrollTooltipRelativeElement={scrollTooltipRelativeElement}
          onScrollHideTooltipCard={onScrollHideTooltipCard}
          mouseLeaveHandler={mouseLeaveHandler}
          isTranslated={isTranslated}
        />
      )}
      {children}
    </div>
  );
};

Tooltip.propTypes = {
  message: string,
  pos: oneOf(['top', 'right', 'bottom', 'left']),
  children: node,
  hide: bool,
  show: bool,
  id: string,
  isHtmlIncluded: bool,
  margin: number,
  tooltipCardInlineCss: oneOfType([object, bool]),
  tooltipCardCustomClass: string,
  scrollTooltipRelativeElement: object,
  onScrollHideTooltipCard: bool,
  useOnPointerLeave: bool,
  customClass: string,
  tooltipParentRole: string,
  tabIndex: oneOfType([string, number]),
  ariaLabel: string,
  arialabelledby: string,
  ariaDescribedby: string,
  ariaHidden: bool,
  isTranslated: bool
};
export default Tooltip;
