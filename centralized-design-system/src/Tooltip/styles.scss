@import '../Styles/_variables';

#portal {
  z-index: 99999;

  .tooltip-msg {
    position: absolute;
    background: var(--ed-black);
    color: var(--ed-white);
    padding: var(--ed-tooltip-padding-y) var(--ed-tooltip-padding-x);
    border-radius: var(--ed-border-radius-md);
    font-size: var(--ed-font-size-supporting);
    max-width: rem-calc(600);
    white-space: normal;
    word-break: break-word;
    z-index: 99999;

    .tooltip-arrow {
      content: '';
      position: absolute;
      z-index: 99999;

      &.top {
        border-left: 0.375rem solid transparent;
        border-right: 0.375rem solid transparent;
        border-top: 0.375rem solid var(--ed-black);
      }

      &.bottom {
        border-left: 0.375rem solid transparent;
        border-right: 0.375rem solid transparent;
        border-bottom: 0.375rem solid var(--ed-black);
      }

      &.left {
        border-left: 0.375rem solid var(--ed-black);
        border-right: 0.375rem solid transparent;
        border-bottom: 0.375rem solid transparent;
        border-top: 0.375rem solid transparent;
      }

      &.right {
        border-left: 0.375rem solid transparent;
        border-right: 0.375rem solid var(--ed-black);
        border-bottom: 0.375rem solid transparent;
        border-top: 0.375rem solid transparent;
      }
    }
  }
}

.ed-tooltip-heading {
  line-height: 0.8rem;
}

.ed-tooltip-heading-width {
  width: 100%;
}

/* Ensure tooltip containers don't interfere with hover events */
.ed-tooltip {
  &.relative {
    position: relative;
  }

  &.inlineflex {
    display: inline-flex;
  }

  /* Ensure proper stacking context for tooltips */
  &:hover {
    z-index: 99999;
  }
}
