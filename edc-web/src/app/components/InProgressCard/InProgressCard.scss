@import '~centralized-design-system/src/Styles/_variables.scss';

.in-progress-card {
  width: 48%;
  height: auto;
  border: var(--ed-border-size-sm) solid var(--ed-border-color);
  border-radius: var(--ed-border-radius-lg);
  background-color: var(--ed-body-bg-color);
  background-size: cover;
  box-shadow: 0 0 0 var(--ed-black);

  &:hover {
    box-shadow: var(--ed-button-shadow);
    /* Ensure child elements can still receive hover events */
    pointer-events: auto;
  }

  /* Ensure tooltip containers can receive hover events */
  .ed-tooltip {
    pointer-events: auto;
    position: relative;
    z-index: 1;
  }

  .card-info {
    .img-block {
      width: 7.125rem;
      height: 4rem;
      border-radius: var(--ed-border-radius-lg);
      background-color: var(--ed-border-color);

      img {
        width: 100%;
        height: 4rem;
        object-fit: cover;
      }
    }

    .info-block {
      .ip-title {
        line-height: 1.375rem;
        max-width: 21.875rem;
      }

      .ip-subtitle {
        line-height: 1.1875rem;
        color: var(--ed-text-color-supporting);

        &.inpogress-card {
          max-width: 15rem;
        }
      }

      .course-details {
        line-height: 0.9375rem;
        color: var(--ed-text-color-supporting);

        .course-block {
          margin-right: 0.3125rem;
          padding-right: 0.3125rem;
          border-right: var(--ed-border-size-sm) solid var(--ed-gray-4);

          i {
            margin: 0 0.1875rem 0 0;
          }

          &:last-child {
            border: none;
          }
        }

        .ip-separator {
          margin: 0 var(--ed-spacing-5xs);
        }
      }
    }
  }

  .ip-progress-bar {
    margin-top: var(--ed-spacing-lg) !important;

    .progress-container .progress-value {
      width: 2rem;
      text-align: left;
    }
  }
}

.in-progress-card.ip-card-full {
  height: 6.25rem;

  .card-info {
    width: 57%;
  }

  .ip-progress-bar {
    width: 43%;
    border-left: var(--ed-border-size-sm) dashed var(--ed-state-disabled-color);
    height: 100%;
    padding-left: var(--ed-spacing-lg);

    .progress-container {
      flex-direction: column;

      .progress-value {
        margin-bottom: var(--ed-spacing-2xs);

        &::after {
          content: attr(data-label);
          margin-left: var(--ed-spacing-5xs);
        }
      }
    }
  }
}

@media only screen and (max-width: #{$breakpoint-md - 1px}) {
  .ed-ui.in-progress-block {
    text-align: center;

    .ip-cardlist {
      overflow-y: auto;
      height: fit-content;
      max-height: rem-calc(464);

      .in-progress-card {
        width: 100%;
        height: auto;

        .card-info {
          .img-block {
            min-width: rem-calc(130);
            margin-right: 0;
          }

          .info-block {
            text-align: left;
            width: calc(100% - 130px);
            padding-left: var(--ed-spacing-base);

            .ip-title {
              width: auto;
              overflow: hidden;
              white-space: normal;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -moz-box-orient: vertical;
              -webkit-line-clamp: 3;
            }

            .ip-subtitle {
              overflow: hidden;
              white-space: normal;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -moz-box-orient: vertical;
              -webkit-line-clamp: 3;
            }
          }
        }

        .course-details {
          padding-top: 0;
          flex-wrap: wrap;

          .course-block {
            margin-right: rem-calc(9);
            margin-top: var(--ed-spacing-2xs);
            padding-right: rem-calc(9);
            border-right: var(--ed-border-size-sm) solid var(--ed-divider-label-color);

            display: flex;
            align-items: center;

            &:last-of-type {
              border-right: none;
            }
          }
        }
      }
    }
  }
}

@media screen and (min-width: $breakpoint-md) {
  .ed-ui.in-progress-block {
    .ip-cardlist.show-scroll {
      height: rem-calc(295);
    }
  }
}

@media only screen and (max-width: #{$breakpoint-xs - 1px}) {
  .ed-ui.in-progress-block {
    .ip-cardlist {
      max-height: rem-calc(551);
    }
  }
}
