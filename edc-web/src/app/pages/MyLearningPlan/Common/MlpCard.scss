@import '~centralized-design-system/src/Styles/_variables.scss';
@import '~styles/base';

.mlp-card-wrapper {
  .mlp-card-block {
    background-color: var(--ed-white);
    border-radius: var(--ed-border-radius-lg);
    box-shadow: var(--ed-shadow-base);

    .in-progress-card-block {
      width: 40%;
      .ed-ui-in-progress-card {
        margin-bottom: 0 !important;
        .card-info {
          .img-block {
            vertical-align: middle;
            width: 6rem;
            height: 4rem;
            border-radius: var(--ed-border-radius-lg);
            img {
              width: 100%;
              height: 100%;
              border-radius: var(--ed-border-radius-lg);
            }
          }
          .info-block {
            & > .ed-tooltip {
              width: 100%;
            }
            .course-details {
              flex-wrap: wrap;
              .course-block {
                margin-bottom: rem-calc(6);
              }
            }
            .ip-subtitle {
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
              max-width: 14rem;
            }
          }
        }
      }
    }

    .gray-text {
      color: var(--ed-text-color-supporting) !important;
    }

    .new-assignee-name {
      cursor: pointer;
      color: var(--ed-primary-base);
    }

    .progress-details-block {
      width: 60%;

      .badge-block {
        width: 2.5rem;
        .badge-image {
          height: 2.5rem;
          width: 2.5rem;
        }
      }

      .progress-status-bar {
        width: 21%;

        .completed-date {
          margin-left: 0.625rem;
        }
      }

      .assignee-name {
        width: 22%;
        margin-left: var(--ed-spacing-lg);
        .ed-tooltip {
          width: 100%;
        }
      }

      .assignment-category {
        width: 20%;
      }

      .progress-date {
        width: 22%;
      }

      .source-block {
        .source-block__title {
          font-weight: var(--ed-font-weight-bold);
        }
      }

      .more-actions {
        display: grid;
        gap: var(--ed-spacing-base);
        grid-template-columns: repeat(3, 1fr);
        justify-content: space-around;
        width: 16%;
        .green-text {
          color: var(--ed-state-active-color);
        }
      }
      .no-cross-icon-width {
        width: 10%;
      }

      .red-text {
        color: var(--ed-negative-2);
      }

      .yellow-text {
        color: var(--ed-warning-2);
      }

      .black-text {
        color: var(--ed-text-color-primary);
      }

      .align-accordion-icon {
        padding-right: 1.094rem;
      }

      .content-status {
        width: 21%;
      }
    }
  }

  .icon-size {
    font-size: 1.375rem;
  }

  .fill-active {
    color: var(--ed-state-active-color);
  }

  .acc-icon {
    font-size: rem-calc(32);
    line-height: 0 !important;
    color: var(--ed-text-color-supporting);
  }

  .accordion-content {
    border-top: var(--ed-border-size-sm) solid var(--ed-state-disabled-color);
  }

  .sub-container {
    border-top: var(--ed-border-size-sm) solid var(--ed-state-disabled-color);
    border-bottom-left-radius: var(--ed-border-radius-lg);
    border-bottom-right-radius: var(--ed-border-radius-lg);
    min-height: 4.25rem;
    background-color: var(--ed-white);
    box-shadow: var(--ed-shadow-base);
    padding: var(--ed-spacing-2xs) 1rem 0.5rem;

    .acordion-tags-container {
      width: 40%;
    }

    .acordion-progress-details {
      width: 60%;

      .empty-space {
        width: 4.4rem;
      }

      .assigned-on {
        width: 26%;
      }

      .started-on {
        width: 63%;
      }
    }
  }
}

[dir='rtl'] {
  .in-progress-card-block .info-block .course-details {
    display: flex;
  }
}

.multi-assignment-flow {
  .sub-container {
    border: none;
    border-radius: 0;
    box-shadow: none;
  }

  .in-progress-card-block {
    width: 65% !important;
  }

  .progress-details-block {
    width: 34% !important;
  }

  .assignee-name,
  .progress-date,
  .badge-block {
    display: none;
  }

  .progress-status-bar {
    width: 45% !important;
  }

  .more-actions {
    width: 34% !important;
  }

  .completed-date {
    margin-left: 2.625rem !important;
  }

  .no-cross-icon-width {
    margin-left: 2rem !important;
    width: 22% !important;
  }

  .info-block {
    flex: 1;
  }

  .err-img-block {
    width: 13.5% !important;
  }

  .assigned-mlp-card-container {
    position: relative;
    padding-top: 0;
    background-color: var(--ed-white);

    .no-assignor {
      margin-right: rem-calc(185);
      column-gap: 6rem;
    }

    .assigned-mlp-card-block {
      padding: var(--ed-spacing-base);

      .assigned-mlp-card-item {
        width: 20.2%;

        & span:first-child {
          color: $waterloo;
        }

        & dt {
          color: $waterloo;
          font-weight: var(--ed-font-weight-normal);
        }

        & dd {
          font-weight: var(--ed-font-weight-normal);
        }

        .red-text {
          color: var(--ed-negative-2);
        }

        .yellow-text {
          color: var(--ed-warning-2);
        }

        .black-text {
          color: var(--ed-text-color-primary);
        }
      }
    }
  }
  .content-status {
    width: 45% !important;
  }
}

.dashed-border {
  height: rem-calc(2);
  overflow: hidden;

  line {
    stroke-width: rem-calc(1);
    stroke: $grayishblue;
    stroke-linecap: butt;
    stroke-linejoin: miter;
    fill: none;
    stroke-dasharray: 5;
  }
}

.disabled-card-wrapper {
  pointer-events: none;
  cursor: not-allowed;

  /* Allow tooltips to work even on disabled cards */
  .ed-tooltip {
    pointer-events: auto;
  }

  .mlp-archived-card {
    opacity: 0.2;

    .align-accordion-icon,
    .more-actions {
      visibility: hidden;
    }
  }

  .mlp-archived-card-text {
    cursor: not-allowed;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--ed-black);
  }
}

.new-learning-history {
  .mlp-card-block {
    border-radius: 0;
  }

  .assigned-mlp-card-container {
    .assigned-mlp-card-block {
      .assigned-mlp-card-item {
        width: 16%;

        &:first-child {
          width: 44%;
        }
      }
    }
  }
}

@media only screen and (max-width: #{$breakpoint-lg - 1px}) {
  .mlp-card-wrapper {
    .mlp-card-block {
      .in-progress-card-block {
        .img-block {
          min-width: 6rem;
          margin-right: var(--ed-spacing-2xs);

          & + .info-block {
            width: calc(100% - 6.5rem);
          }
        }

        .err-img-block {
          min-width: 6rem;

          & + .info-block {
            width: calc(100% - 6rem);
          }
        }
      }

      .progress-details-block {
        .more-actions {
          flex: 1;
          justify-content: flex-end;

          & > div {
            padding: 0 var(--ed-spacing-2xs);
          }
        }
      }
    }

    .sub-container {
      flex-wrap: wrap;

      .acordion-progress-details {
        .empty-space {
          width: 3.6rem;
        }
      }

      .accordion-progress-status {
        width: 40%;
        display: flex;

        .progress-date {
          &.red-text {
            color: var(--ed-negative-2);
          }

          &.yellow-text {
            color: var(--ed-warning-2);
          }

          &.black-text {
            color: var(--ed-text-color-primary);
          }
        }

        & > div:last-child {
          margin-left: rem-calc(30);
          max-width: rem-calc(130);
          flex: 1;

          .progress-status-bar {
            margin-top: var(--ed-spacing-2xs);

            .progress-value {
              text-align: left;
              margin-right: 0;
            }
          }
        }
      }
    }
  }

  .multi-assignment-flow {
    .sub-container {
      flex-direction: column;
    }

    .accordion-progress-status {
      padding-top: var(--ed-spacing-base);

      .progress-status-bar {
        width: 100% !important;
      }
    }

    .completed-date {
      margin-left: 0 !important;
    }

    .in-progress-card-block {
      width: 58% !important;
    }

    .progress-details-block {
      width: 42% !important;
    }
  }

  .new-learning-history {
    .assigned-mlp-card-container {
      .assigned-mlp-card-block {
        .assigned-mlp-card-item {
          width: 20%;

          &:first-child {
            width: 48%;
          }
        }
      }
    }
  }
}

@media only screen and (max-width: #{$breakpoint-md - 1px}) {
  .mlp-card-wrapper {
    .ed-ui-in-progress-card {
      .card-info {
        .err-img-block {
          width: fit-content;
        }
      }
    }

    .sub-container {
      .acordion-progress-details {
        position: relative;

        .assigned-on {
          flex: 1;
        }

        .started-on {
          position: absolute;
          left: 3.6rem;
          width: calc(100% - 3.6rem);
          transform: translateY(100%);
        }
      }
    }
  }
}

@media only screen and (max-width: #{$breakpoint-xs - 1px}) {
  .mlp-card-wrapper {
    .sub-container {
      .acordion-tags-container {
        width: 100%;
        margin-bottom: var(--ed-spacing-2xs);
      }

      .acordion-progress-details {
        margin-bottom: var(--ed-spacing-2xs);
        width: 100%;

        .empty-space {
          display: none;
        }

        .assigned-on {
          flex: 1;
        }

        .started-on {
          flex: 1;
          position: unset;
          transform: translateY(0);
        }
      }

      .accordion-progress-status {
        width: 50%;
        margin-bottom: var(--ed-spacing-2xs);
        & > div {
          flex: 1;

          &:last-child {
            max-width: none;
            margin-left: 0;
          }
        }
      }
    }

    .mlp-card-block {
      flex-wrap: wrap;
      position: relative;

      .in-progress-card-block {
        width: 100%;

        .info-block {
          .ip-title,
          .ip-subtitle {
            width: calc(100% - 3rem);
          }

          .course-details {
            flex-wrap: wrap;
            padding-top: 0;

            .course-block {
              margin-top: var(--ed-spacing-2xs);
              margin-bottom: 0;
            }
          }
        }
      }

      .progress-details-block {
        width: 100%;
        padding-bottom: var(--ed-spacing-base);

        .badge-block {
          top: var(--ed-spacing-base);
          right: 1rem;
          position: absolute;
        }

        .assignee-name {
          display: none;
        }

        .more-actions {
          justify-content: space-evenly;
        }
      }
    }

    &.multi-assignment-flow {
      .in-progress-card-block {
        width: 100% !important;
      }

      .progress-details-block {
        width: 100% !important;
      }
    }
  }
}
