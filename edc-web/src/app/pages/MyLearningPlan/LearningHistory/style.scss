@import '~centralized-design-system/src/Styles/_variables.scss';

.ed-ui.learning-history-plan {


  .filter-wrapper {
    .ed-btn {
      background-color: var(--ed-white);
      padding: 0.5625rem;
      .icon-new-filter {
        color: var(--ed-text-color-supporting);
      }
    }
    .text-color {
      color: var(--ed-text-color-primary);
    }
  }
  .excel-btn-wrapper {
    .ed-btn {
      min-width: 11.25rem;
      height: 2.375rem;
      background-color: var(--ed-white);
    }
  }
  .empty-content {
    color: var(--ed-text-color-primary);
  }
  .min-content-height {
    min-height: 16.625rem;
  }
}
.serach-box-container {
  max-width: 31.25rem;
  flex: 1;
  .ed-btn {
    max-width: 3.25rem;
    min-width: 3.25rem;
    background-color: var(--ed-white);
    .icon-search {
      color: var(--ed-text-color-supporting);
    }
  }
}

.assignment-container {
  max-width: 16.25rem;
}

.sorting-container {
  background-color: var(--ed-white);
  height: 2.375rem;
  width: 100%;
  box-shadow: var(--ed-shadow-base);
  border-radius: var(--ed-border-radius-lg);
  .name-container {
    width: 57%;
    margin-left: 1.125rem;
  }
  .assigned-by-container {
    width: 22%;
  }
  .due-date-container {
    width: 37%;
    padding-left: 0.2rem;
  }
  .dark-text {
    color: var(--ed-text-color-primary);
  }
  .sort-icon {
    &:hover {
      color: var(--ed-primary-base);
    }
  }
}

@media only screen and (max-width: #{$breakpoint-lg - 1px}) {
  .ed-ui.learning-history-plan {
    .filter-wrapper {
      flex-wrap: wrap;
      .serach-box-container {
        max-width: none;
        min-width: 20rem;
      }
      .assignment-container {
        max-width: none;
        flex: 1;
        min-width: 30rem;
        margin-left: 0;
        margin-top: var(--ed-spacing-2xs);

        .ed-input-container {
          max-width: none;
        }
      }
      .excel-btn-wrapper {
        margin-top: var(--ed-spacing-2xs);
      }
    }
    .sorting-container {
      .name-container {
        width: 43.5%;
      }
      .assigned-by-container {
        flex: 1;
      }
      .due-date-container {
        display: none;
      }
    }
  }
}

@media only screen and (max-width: #{$breakpoint-md - 1px}) {
  .ed-ui.learning-history-plan {
    .filter-wrapper {
      .serach-box-container {
        min-width: 18rem;
      }
    }
  }
}

@media only screen and (max-width: #{$breakpoint-sm - 1px}) {
  .ed-ui.learning-history-plan {
    .filter-wrapper {
      padding: 0 var(--ed-spacing-base);
      .filter-button-wrapper {
        order: 1;
      }
      .sort-container {
        order: 2;
        flex: 1;
        .sort-n-year-box {
          width: 100%;
        }
      }
      .excel-btn-wrapper {
        order: 3;
        margin-top: 0;
      }
      .serach-box-container {
        order: 4;
        margin: var(--ed-spacing-2xs) 0 0 0;
      }
      .assignment-container {
        order: 5;
      }
      @media only screen and (min-width: $breakpoint-xs) and (max-width: 631px) {
        .filter-button-wrapper {
          .ed-btn {
            height: 100%;
          }
        }
        .sort-container {
          .sort-n-year-box {
            min-height: 100%;
            align-items: center;
          }
        }
        .excel-btn-wrapper {
          flex: 1;
          min-width: 5rem;
          .ed-btn {
            width: 100%;
            min-width: auto;
            height: auto;
          }
        }
      }
    }
  }
}

@media only screen and (max-width: #{$breakpoint-xs - 1px}) {
  .ed-ui.learning-history-plan {
    .filter-wrapper {
      .filter-button-wrapper {
        .ed-btn {
          height: 100%;
        }
      }
      .excel-btn-wrapper {
        order: 2;
        flex: 1;
        .ed-btn {
          width: 100%;
          min-width: 10rem;
          padding-left: rem-calc(6);
          padding-right: rem-calc(6);
        }
      }
      .sort-container {
        order: 3;
        margin: var(--ed-spacing-2xs) 0 0 0;
      }
      .serach-box-container,
      .assignment-container {
        min-width: 100%;
      }
    }
  }
}
