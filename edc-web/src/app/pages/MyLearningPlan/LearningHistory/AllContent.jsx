import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { string, number, object, bool, func } from 'prop-types';
import { getUserContentCompletions } from 'edc-web-sdk/requests/cards.v2';
import Loading from 'centralized-design-system/src/Loading';
import { translatr } from 'centralized-design-system/src/Translatr';
import moment from 'moment';
import classNames from 'classnames';

import MlpCard from '../Common/MlpCard';
import { FETCH_RECORDS_LIMIT, ELIGIBLE_CARD_TYPES } from '../constant';
import { open_v2 as openSnackBar } from '../../../../app/actions/snackBarActions';
import UserdashboardErrorBoundary from '@components/UserdashboardErrorBoundary/UserdashboardErrorBoundary';
import { getFullLanguage } from '../Common/constants';

const AllContent = ({
  activeYear,
  queryText,
  isDownloadExcel,
  setIsDownloadExcel,
  dispatch,
  filter,
  mlpConfig,
  isMultiAssignmentFlow,
  learningHistoryContentType,
  setdisableExcelButton,
  showCompletionHistory
}) => {
  const {
    assignedDate,
    startedDate,
    completedDate,
    assignmentTypeOptions,
    sortData,
    assignedBy
  } = filter;

  const getFormattedDate = date => moment(date).format('MM/DD/YYYY');

  const [userCompletedContent, setUserCompletedContent] = useState({
    cards: [],
    totalCount: 0,
    isViewMore: false
  });
  const [isLoading, setIsLoading] = useState(true);

  const getConfiguredCardTypes = () => {
    const contentType = mlpConfig?.['web/mlp/myAssignments']?.['defaultValue'];
    let card_types = [];
    if (contentType === 'notAllContentType') {
      card_types = [...ELIGIBLE_CARD_TYPES];
    }
    return card_types;
  };

  const getPayload = async offset => {
    let payload = {
      limit: FETCH_RECORDS_LIMIT,
      offset: offset,
      year_filter: activeYear,
      fields:
        'assignment,assigner,badging,completed_at,tags,started_at,due_at, resource, default_language',
      'card_types[]': getConfiguredCardTypes(),
      state: 'completed'
    };
    if (isDownloadExcel) payload.download_csv = 'true';
    if (isMultiAssignmentFlow) {
      payload.multi_assignments_flow = true;
      payload.fields += ',assignment_count';
    }
    const filterOptionPayload = getFilterOptionPayload();
    if (isMultiAssignmentFlow && showCompletionHistory) {
      /*
        send completion_history in payload when multi assignment and completion history is enabled 
      */
      payload.completion_history = true;
      /**
       * When Show completion history & Multi Assignments flow is enabled, start date filter should be skipped
       * Hence deleting the start date filter option from the payload
       **/
      delete filterOptionPayload?.started_from_date;
      delete filterOptionPayload?.started_to_date;
    }
    return { ...payload, ...filterOptionPayload };
  };

  const getFilterOptionPayload = () => {
    const payload = {
      order: sortData?.field ? sortData?.order : 'desc',
      sort:
        isMultiAssignmentFlow && sortData?.field == 'card_duration'
          ? '-card_duration'
          : sortData?.field || 'id',
      'assignment_priority[]': assignmentTypeOptions || [],
      'assignor_ids[]': assignedBy?.map(item => item.id) || []
    };

    if (startedDate?.start) payload.started_from_date = getFormattedDate(startedDate?.start);
    if (startedDate?.end) payload.started_to_date = getFormattedDate(startedDate?.end);
    if (assignedDate?.start) payload.assigned_from_date = getFormattedDate(assignedDate?.start);
    if (assignedDate?.end) payload.assigned_to_date = getFormattedDate(assignedDate?.end);
    if (completedDate?.start)
      payload.completed_from_date = getFormattedDate(completedDate?.from || completedDate?.start);
    if (completedDate?.end) payload.completed_to_date = getFormattedDate(completedDate?.end);
    if (queryText) payload.query = queryText;

    return payload;
  };

  const getContentCompletionsData = async ({ offset, isViewMore }) => {
    setIsLoading(true);
    const payload = await getPayload(offset);
    getUserContentCompletions(payload)
      .then(data => {
        if (!isDownloadExcel) {
          if (isViewMore) {
            setUserCompletedContent({
              cards: userCompletedContent.cards.concat(data.cards),
              totalCount: data.total,
              isViewMore: false
            });
          } else {
            setUserCompletedContent({
              cards: data.cards,
              totalCount: data.total,
              isViewMore: false
            });
          }
        } else {
          dispatch(
            openSnackBar(
              translatr('web.mylearningplan.main', 'CsvFileBeingProcessedAndWillBeEmailedShortly'),
              'info',
              false
            )
          );
          setIsDownloadExcel(false);
        }
        setIsLoading(false);
      })
      .catch(err => console.error(err));
  };

  useEffect(() => {
    setdisableExcelButton(!isLoading && !userCompletedContent.cards.length);
  });

  useEffect(() => {
    if (!!Object.keys(filter)?.length) getContentCompletionsData({ offset: 0, isViewMore: false });
  }, [queryText, isDownloadExcel, filter]);

  const renderEmptyComponent = () => {
    return (
      <p className="font-size-xl empty-content justflex justify-center min-content-height">
        {translatr('web.mylearningplan.main', 'MyLeaningPlanEmptyComponentMsg')}
      </p>
    );
  };

  const handleViewMore = () => {
    getContentCompletionsData({ offset: userCompletedContent.cards.length, isViewMore: true });
  };

  const renderViewMore = () => {
    return (
      <div className="make-center">
        <button
          className="ed-btn ed-btn-neutral color-white text-center m-margin-top"
          onClick={handleViewMore}
        >
          {translatr('web.mylearningplan.main', 'ViewMore')}
        </button>
      </div>
    );
  };

  const renderShowMore = () => {
    return (
      <div className="supporting-text no-padding m-margin-top text-center">
        {translatr('web.mylearningplan.main', 'ShowingCurrentcountOutOfTotalcount', {
          currentCount: userCompletedContent.cards.length,
          totalCount: userCompletedContent.totalCount
        })}
      </div>
    );
  };

  const handleBookmark = (id, isBookmarked) => {
    const index = userCompletedContent.cards.findIndex(item => item.id === id);
    const updatedObj = {
      ...userCompletedContent.cards[index],
      isBookmarked: !isBookmarked
    };
    const newArray = [...userCompletedContent.cards];
    newArray[index] = updatedObj;

    setUserCompletedContent({
      ...userCompletedContent,
      cards: newArray,
      totalCount: userCompletedContent.totalCount
    });
  };

  const updateCard = id => {
    setIsLoading(true);
    const currentOffset = userCompletedContent.cards.findIndex(card => card.id === id);
    const updatedCards = [...userCompletedContent.cards];
    updatedCards.splice(currentOffset, 1);
    setUserCompletedContent({
      ...userCompletedContent,
      cards: updatedCards,
      totalCount: userCompletedContent.totalCount - 1
    });
    setIsLoading(false);
  };

  const getCompletedOn = ({ assignment, completedAt }) => {
    /*
     * when multi_assignments_flow & show_completion_history is enabled & show, 1st preference is cards completedAt
     * when either is disabled, check assignments completed_at or cards completedAt
     * keeping behaviour as it is for single assignments
     */

    if (isMultiAssignmentFlow && showCompletionHistory) {
      return completedAt;
    }

    return assignment?.completed_at || completedAt;
  };

  return (
    /*
    Todo: Few fields are missing in api response.
    Need to integrate those fields once api issue is resolved.
    */
    <>
      {!isLoading &&
        userCompletedContent.cards?.map((item, index) => {
          return (
            <MlpCard
              key={`${item.id}-${index}-${userCompletedContent.cards.length}`} // Unique key to force re-render
              id={item.id}
              card={item}
              title={item.cardTitle}
              assignmentCount={item.assignmentCount}
              type={item.readableCardType}
              duration={item.eclDurationMetadata?.calculated_duration_display}
              author={item.author ? item.author?.fullName : item.eclSourceDisplayName}
              assigneeName={item.assigner?.fullName}
              assigneeId={item.assigner?.id}
              completedOn={getCompletedOn(item)}
              skillLevel={item.skillLevel}
              startedOn={item.assignment ? item.assignment.started_at : item.startedAt}
              language={getFullLanguage(item.language)}
              siteName={item.resource?.siteName}
              assignedOn={item.assignment?.assigned_date}
              badgeUrl={item.badging?.imageUrl}
              completedPercentage={item.completedPercentage}
              tags={item.tags}
              isBookmark={item.isBookmarked}
              dueAt={null}
              state={item.state}
              tabName="LearningHistory"
              year={activeYear}
              isMultiAssignmentFlow={isMultiAssignmentFlow}
              updateCard={() => updateCard(item.id)}
              learningHistoryContentType={learningHistoryContentType}
              filter={filter}
              getFilterOptionPayload={getFilterOptionPayload}
              unBookmakred={() => handleBookmark(item.id, item.isBookmarked)}
              bookmarked={() => handleBookmark(item.id, item.isBookmarked)}
              showCompletionHistory={showCompletionHistory}
            />
          );
        })}
      {!isLoading && !userCompletedContent.cards.length && renderEmptyComponent()}
      {isLoading && (
        <div className={classNames({ 'min-content-height': !userCompletedContent.cards.length })}>
          <Loading />
        </div>
      )}
      {userCompletedContent.totalCount > 5 &&
        userCompletedContent.totalCount !== userCompletedContent.cards.length &&
        !isLoading &&
        renderViewMore()}
      {!isLoading && renderShowMore()}
    </>
  );
};

AllContent.propTypes = {
  isDownloadExcel: bool,
  activeYear: number,
  filter: object,
  mlpConfig: object,
  queryText: string,
  isMultiAssignmentFlow: bool,
  setIsDownloadExcel: func,
  isOffsetChanged: bool,
  learningHistoryContentType: string,
  setdisableExcelButton: func,
  showCompletionHistory: bool
};

const mapStateToProps = ({ team }) => {
  const mlpConfig = team.get('OrgConfig').mlp;
  return {
    mlpConfig,
    isMultiAssignmentFlow: team.get('config').multi_assignments_flow
  };
};

export default UserdashboardErrorBoundary(connect(mapStateToProps)(AllContent));
