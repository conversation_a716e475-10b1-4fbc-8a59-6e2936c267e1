import { getCardImage } from '@utils/smartCardUtilsV2';

// Global state to track preloaded images across the application
const globalPreloadedImages = new Set();
const globalPriorityImages = new Set();

/**
 * Preload images from search results to reduce loading delay
 * This function should be called after search API calls complete
 * @param {Array} cards - Array of card objects from search results
 * @param {Object} options - Preloading options
 * @param {number} options.priorityCount - Number of priority images to preload immediately (default: 12)
 * @param {number} options.maxConcurrent - Maximum concurrent preloads (default: 6)
 * @param {boolean} options.background - Whether to preload remaining images in background (default: true)
 * @returns {Promise} - Promise that resolves when priority images are preloaded
 */
export const preloadSearchImages = async (cards = [], options = {}) => {
  const {
    priorityCount = 12,
    maxConcurrent = 6,
    background = true
  } = options;

  if (!Array.isArray(cards) || cards.length === 0) {
    return Promise.resolve();
  }

  // Extract image URLs from cards
  const imageUrls = [];

  for (const card of cards) {
    try {
      const imageData = getCardImage(card);
      const imageUrl = imageData?.img || imageData;

      if (imageUrl && typeof imageUrl === 'string' && !globalPreloadedImages.has(imageUrl)) {
        imageUrls.push(imageUrl);
      }
    } catch (error) {
      console.warn('Failed to extract image URL from card:', error);
    }
  }

  if (imageUrls.length === 0) {
    console.log('No images found in search results to preload');
    return Promise.resolve();
  }

  console.log(`Starting preload of ${imageUrls.length} images from search results`);

  // Split into priority and background images
  const priorityImages = imageUrls.slice(0, priorityCount);
  const backgroundImages = imageUrls.slice(priorityCount);

  /**
   * Preload a single image
   * @param {string} imageUrl - URL of the image to preload
   * @returns {Promise} - Promise that resolves when image is loaded
   */
  const preloadImage = (imageUrl) => {
    return new Promise((resolve, reject) => {
      const img = new Image();

      img.onload = () => {
        globalPreloadedImages.add(imageUrl);
        resolve(imageUrl);
      };

      img.onerror = () => {
        reject(new Error(`Failed to preload: ${imageUrl}`));
      };

      // Set a timeout to avoid hanging on slow images
      const timeout = setTimeout(() => {
        reject(new Error(`Timeout preloading: ${imageUrl}`));
      }, 10000); // 10 second timeout

      img.onload = () => {
        clearTimeout(timeout);
        resolve(imageUrl);
      };

      img.onerror = () => {
        clearTimeout(timeout);
        reject(new Error(`Failed to preload: ${imageUrl}`));
      };

      img.src = imageUrl;
    });
  };

  /**
   * Preload images in batches with concurrency control
   * @param {Array} urls - Array of image URLs
   * @param {number} concurrent - Number of concurrent preloads
   * @returns {Promise} - Promise that resolves when all batches complete
   */
  const preloadBatch = async (urls, concurrent = maxConcurrent) => {
    const results = [];

    for (let i = 0; i < urls.length; i += concurrent) {
      const batch = urls.slice(i, i + concurrent);
      const batchPromises = batch.map(url =>
        preloadImage(url).catch(error => {
          console.warn(`Image preload failed: ${error.message}`);
          return null; // Don't fail the entire batch
        })
      );

      const batchResults = await Promise.allSettled(batchPromises);
      results.push(...batchResults);
    }

    return results;
  };

  try {
    // Mark priority images in global state
    priorityImages.forEach(url => globalPriorityImages.add(url));

    // Preload priority images first and wait for them
    console.log(`Preloading ${priorityImages.length} priority images`);
    await preloadBatch(priorityImages, maxConcurrent);
    console.log('Priority images preloaded successfully');

    // Preload remaining images in background if enabled
    if (background && backgroundImages.length > 0) {
      console.log(`Starting background preload of ${backgroundImages.length} additional images`);

      // Use fewer concurrent connections for background loading
      const backgroundConcurrent = Math.max(2, Math.floor(maxConcurrent / 2));

      preloadBatch(backgroundImages, backgroundConcurrent)
        .then(() => {
          console.log('Background image preloading completed');
        })
        .catch(error => {
          console.warn('Background image preloading failed:', error);
        });
    }

    return Promise.resolve();
  } catch (error) {
    console.error('Priority image preloading failed:', error);
    throw error;
  }
};

/**
 * Preload images for promoted content
 * @param {Array} promotedCards - Array of promoted content cards
 * @returns {Promise} - Promise that resolves when images are preloaded
 */
export const preloadPromotedImages = async (promotedCards = []) => {
  if (!Array.isArray(promotedCards) || promotedCards.length === 0) {
    return Promise.resolve();
  }

  console.log(`Preloading images for ${promotedCards.length} promoted content items`);

  return preloadSearchImages(promotedCards, {
    priorityCount: promotedCards.length, // All promoted content is priority
    maxConcurrent: 4,
    background: false // No background loading for promoted content
  });
};

/**
 * Create a search-specific image preloader that can be called from search components
 * @param {string} searchTerm - Current search term for logging
 * @param {string} activeTab - Active search tab
 * @returns {Function} - Function to trigger image preloading
 */
export const createSearchImagePreloader = (searchTerm, activeTab) => {
  return async (searchResults, isPromoted = false) => {
    if (activeTab !== 'card') {
      return; // Only preload for card search results
    }

    const searchId = `${searchTerm}_${activeTab}_${Date.now()}`;
    console.log(`Triggering image preload for search: "${searchTerm}" (${searchId})`);

    try {
      if (isPromoted) {
        await preloadPromotedImages(searchResults);
      } else {
        await preloadSearchImages(searchResults, {
          priorityCount: 12,
          maxConcurrent: 6,
          background: true
        });
      }
    } catch (error) {
      console.error(`Image preloading failed for search "${searchTerm}":`, error);
    }
  };
};

/**
 * Check if an image has been preloaded
 * @param {string} imageUrl - The image URL to check
 * @returns {boolean} - True if image has been preloaded
 */
export const isImagePreloaded = (imageUrl) => {
  return globalPreloadedImages.has(imageUrl);
};

/**
 * Check if an image should use priority loading
 * @param {string} imageUrl - The image URL to check
 * @returns {boolean} - True if image should use priority loading
 */
export const isImagePriority = (imageUrl) => {
  return globalPriorityImages.has(imageUrl) || globalPreloadedImages.has(imageUrl);
};

/**
 * Get image priority for a card
 * @param {Object} card - Card object
 * @returns {boolean} - True if card image should use priority loading
 */
export const getCardImagePriority = (card) => {
  try {
    const imageData = getCardImage(card);
    const imageUrl = imageData?.img || imageData;
    return imageUrl ? isImagePriority(imageUrl) : false;
  } catch (error) {
    return false;
  }
};

/**
 * Clear all preloaded images cache
 */
export const clearPreloadedImages = () => {
  globalPreloadedImages.clear();
  globalPriorityImages.clear();
};

export default preloadSearchImages;
